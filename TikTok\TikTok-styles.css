/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: #0f0f0f;
    min-height: 100vh;
    color: #ffffff;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 15px 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.5rem;
    font-weight: 700;
    color: #e91e63;
}

.nav-logo i {
    font-size: 2rem;
    background: linear-gradient(45deg, #000000, #ff0050);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.nav-link:hover {
    background: rgba(233, 30, 99, 0.1);
    color: #e91e63;
}

/* Main Container */
.main-container {
    padding-top: 70px;
    min-height: 100vh;
}

/* Step Sections */
.step-section {
    display: none;
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: calc(100vh - 70px);
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s ease;
}

.step-section.active {
    display: flex;
    animation: slideInUp 0.8s ease forwards;
}

@keyframes slideInUp {
    0% {
        opacity: 0;
        transform: translateY(50px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.step-container {
    background: #1a1a1a;
    border: 1px solid #333333;
    border-radius: 16px;
    padding: 40px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    text-align: center;
    width: 100%;
}

/* Step Header */
.step-header {
    margin-bottom: 40px;
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #ff0050, #000000);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 auto 20px;
}

.step-header h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 10px;
}

.step-header p {
    color: #a0a0a0;
    font-size: 1.1rem;
}

/* Search Form */
.search-form {
    margin-bottom: 40px;
}

.input-group {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.input-wrapper {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
}

.input-wrapper i {
    position: absolute;
    left: 15px;
    color: #666;
    font-size: 1.2rem;
}

.input-wrapper input {
    width: 100%;
    padding: 15px 15px 15px 45px;
    border: 2px solid #e1e8ed;
    border-radius: 50px;
    font-size: 1rem;
    font-family: 'Poppins', sans-serif;
    transition: all 0.3s ease;
}

.input-wrapper input:focus {
    outline: none;
    border-color: #e91e63;
    box-shadow: 0 0 0 3px rgba(233, 30, 99, 0.1);
}

.btn {
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    font-family: 'Poppins', sans-serif;
}

.btn-primary {
    background: linear-gradient(45deg, #000000, #ff0050);
    color: white;
    box-shadow: 0 10px 30px rgba(255, 0, 80, 0.3);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(255, 0, 80, 0.4);
}

.btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-3px);
}

.btn-large {
    padding: 20px 40px;
    font-size: 1.2rem;
}

.input-hint {
    color: #666;
    font-size: 0.9rem;
}

/* Loading Container */
.loading-container {
    text-align: center;
    padding: 40px 20px;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #e91e63;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Container */
.error-container {
    text-align: center;
    padding: 40px 20px;
}

.error-icon {
    font-size: 3rem;
    color: #dc3545;
    margin-bottom: 20px;
}

.error-container h3 {
    color: #333;
    margin-bottom: 15px;
}

.error-container p {
    color: #666;
    margin-bottom: 25px;
}

/* Profile Display */
.profile-display {
    margin-bottom: 40px;
}

.profile-card {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
}

.profile-image-container {
    margin-bottom: 20px;
}

.profile-image {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 4px solid #e91e63;
    object-fit: cover;
}

.profile-info h3 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 5px;
}

.profile-info p {
    color: #666;
    margin-bottom: 20px;
}

.profile-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
}

/* Package Selection */
.package-selection h3 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 30px;
}

.packages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.package-card {
    background: #f8f9fa;
    border: 2px solid #e1e8ed;
    border-radius: 15px;
    padding: 25px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.package-card:hover {
    border-color: #e91e63;
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(233, 30, 99, 0.2);
}

.package-card.selected {
    border-color: #e91e63;
    background: linear-gradient(135deg, rgba(240, 148, 51, 0.1), rgba(233, 30, 99, 0.1));
}

.package-card.popular {
    border-color: #f09433;
    background: linear-gradient(135deg, rgba(240, 148, 51, 0.1), rgba(233, 30, 99, 0.1));
}

.popular-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(45deg, #f09433, #e91e63);
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.package-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #f09433, #e91e63);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: white;
    font-size: 1.2rem;
}

.package-card h4 {
    font-size: 1.1rem;
    color: #333;
    margin-bottom: 10px;
}

.package-followers {
    font-size: 1.5rem;
    font-weight: 700;
    color: #e91e63;
    margin-bottom: 10px;
}

.package-card p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.package-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #333;
}

/* Custom Amount */
.custom-amount {
    margin: 30px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 15px;
}

.custom-amount h4 {
    color: #333;
    margin-bottom: 15px;
}

.custom-input-group {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    max-width: 300px;
    margin: 0 auto;
}

.custom-input-group input {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid #e1e8ed;
    border-radius: 25px;
    font-size: 1rem;
    text-align: center;
}

.custom-input-group input:focus {
    outline: none;
    border-color: #e91e63;
}

.input-suffix {
    color: #666;
    font-weight: 500;
}

/* Boosting Display */
.boosting-display {
    text-align: center;
}

.profile-preview {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
}

.follower-counter {
    margin-top: 20px;
}

.counter-display {
    margin-bottom: 20px;
}

.counter-number {
    font-size: 3rem;
    font-weight: 700;
    color: #e91e63;
    display: block;
    margin-bottom: 5px;
    animation: countUp 0.5s ease;
}

@keyframes countUp {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

.counter-label {
    color: #666;
    font-size: 1.1rem;
}

.progress-bar {
    width: 100%;
    height: 10px;
    background: #e1e8ed;
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(45deg, #f09433, #e91e63);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 5px;
}

.progress-text {
    display: flex;
    justify-content: space-between;
    color: #666;
    font-size: 0.9rem;
}

.boosting-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
}

.stat-box {
    background: white;
    border: 2px solid #e1e8ed;
    border-radius: 15px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-box i {
    font-size: 2rem;
    color: #e91e63;
}

.stat-info {
    text-align: left;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.stat-box .stat-label {
    color: #666;
    font-size: 0.9rem;
}

/* Verification Display */
.verification-display {
    text-align: center;
    max-width: 500px;
    margin: 0 auto;
}

.warning-icon {
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, #ffc107, #ff9800);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 30px;
    font-size: 3rem;
    color: white;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.verification-display h3 {
    font-size: 1.8rem;
    color: #333;
    margin-bottom: 20px;
}

.verification-message {
    color: #666;
    line-height: 1.6;
    margin-bottom: 30px;
    font-size: 1.1rem;
}

.verification-info {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    text-align: left;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    color: #666;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-item i {
    color: #28a745;
    font-size: 1.2rem;
}

.btn-verify {
    background: #6c757d;
    color: white;
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    cursor: not-allowed;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    opacity: 0.6;
}

.verification-note {
    color: #666;
    font-size: 0.9rem;
    font-style: italic;
}

/* Animations */
.slide-in-left {
    animation: slideInLeft 0.6s ease forwards;
}

.slide-in-right {
    animation: slideInRight 0.6s ease forwards;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.bounce-in {
    animation: bounceIn 0.8s ease forwards;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Comments Section */
.comments-section {
    padding: 60px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e1e8ed;
}

.comments-container {
    max-width: 800px;
    margin: 0 auto;
}

.comments-header {
    text-align: center;
    margin-bottom: 40px;
}

.comments-header h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.comments-header h3 i {
    color: #e91e63;
}

.comments-header p {
    color: #666;
    font-size: 1rem;
}

.comments-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-height: 500px;
    overflow-y: auto;
    padding: 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.comment-item {
    display: flex;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
    animation: slideInComment 0.5s ease forwards;
    opacity: 0;
    transform: translateY(20px);
}

.comment-item:hover {
    background: #e3f2fd;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

@keyframes slideInComment {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.comment-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e91e63;
}

.comment-content {
    flex: 1;
}

.comment-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.comment-username {
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
}

.verified-badge {
    color: #1da1f2;
    font-size: 0.9rem;
}

.comment-time {
    color: #999;
    font-size: 0.85rem;
    margin-left: auto;
}

.comment-text {
    color: #555;
    line-height: 1.5;
    font-size: 0.95rem;
}

.comments-loading {
    text-align: center;
    padding: 20px;
    display: none;
}

.comments-loading.show {
    display: block;
}

.comments-loading .loading-spinner {
    width: 30px;
    height: 30px;
    margin: 0 auto 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .step-container {
        padding: 25px;
    }

    .input-group {
        flex-direction: column;
    }

    .profile-stats {
        gap: 20px;
        flex-wrap: wrap;
    }

    .packages-grid {
        grid-template-columns: 1fr;
    }

    .custom-input-group {
        flex-direction: column;
    }

    .boosting-stats {
        grid-template-columns: 1fr;
    }

    .stat-box {
        justify-content: center;
        text-align: center;
    }

    .stat-info {
        text-align: center;
    }

    .counter-number {
        font-size: 2.5rem;
    }

    .verification-info {
        text-align: center;
    }

    .info-item {
        justify-content: center;
    }

    .comments-section {
        padding: 40px 15px;
    }

    .comment-item {
        gap: 10px;
        padding: 12px;
    }

    .comment-avatar {
        width: 40px;
        height: 40px;
    }

    .profile-header {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .profile-avatar {
        width: 100px;
        height: 100px;
    }

    .profile-stats {
        justify-content: center;
        gap: 20px;
    }
}

/* TikTok Profile Display */
.tiktok-profile-display {
    margin-bottom: 40px;
}

.tiktok-profile-card {
    background: #1a1a1a;
    border: 1px solid #333333;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 30px;
}

.profile-header {
    padding: 30px;
    display: flex;
    align-items: flex-start;
    gap: 30px;
}

.profile-avatar-section {
    flex-shrink: 0;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #ff0050;
    box-shadow: 0 0 20px rgba(255, 0, 80, 0.3);
}

.profile-info {
    flex: 1;
    min-width: 0;
}

.profile-name-section {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.profile-name {
    font-size: 1.75rem;
    font-weight: 700;
    color: #ffffff;
    margin: 0;
}

.verified-badge {
    color: #ff0050;
    font-size: 1.2rem;
}

.profile-username {
    font-size: 1rem;
    color: #a0a0a0;
    margin-bottom: 20px;
}

.profile-stats {
    display: flex;
    gap: 30px;
    margin-bottom: 20px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-number {
    font-size: 1.125rem;
    font-weight: 700;
    color: #ffffff;
    line-height: 1.2;
}

.stat-label {
    font-size: 0.875rem;
    color: #a0a0a0;
    margin-top: 2px;
    text-transform: capitalize;
}

.profile-bio {
    margin-bottom: 20px;
}

.profile-bio p {
    font-size: 1rem;
    line-height: 1.5;
    color: #ffffff;
    margin: 0;
}

.profile-actions {
    display: flex;
    gap: 8px;
}

.follow-btn, .message-btn, .more-btn {
    padding: 10px 20px;
    border: 1px solid #333333;
    border-radius: 8px;
    background: #1a1a1a;
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.follow-btn {
    background: linear-gradient(45deg, #ff0050, #ff4081);
    color: white;
    border-color: #ff0050;
}

.follow-btn:hover {
    background: linear-gradient(45deg, #e6004a, #ff1744);
    transform: translateY(-1px);
}

.message-btn:hover, .more-btn:hover {
    background: #333333;
    border-color: #555555;
}

.more-btn {
    padding: 10px 15px;
}

/* Live Profile Display */
.live-profile-display {
    margin-bottom: 30px;
}

.live-profile-display .tiktok-profile-card {
    animation: profilePulse 2s infinite;
}

@keyframes profilePulse {
    0%, 100% {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
    50% {
        box-shadow: 0 8px 25px rgba(255, 0, 80, 0.4);
    }
}

/* Free Boost Section */
.free-boost-section {
    text-align: center;
    padding: 40px 20px;
    background: linear-gradient(135deg, rgba(255, 0, 80, 0.1), rgba(0, 0, 0, 0.3));
    border-radius: 20px;
    margin: 30px 0;
    border: 1px solid #333333;
}

.free-badge {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(45deg, #000000, #ff0050);
    color: white;
    padding: 12px 24px;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 20px;
    animation: pulse 2s infinite;
}

.free-badge i {
    font-size: 1.3rem;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.free-boost-section h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 15px;
}

.free-boost-section p {
    font-size: 1.1rem;
    color: #8b98a5;
    margin-bottom: 30px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* Follower Options */
.follower-options {
    margin: 30px 0;
}

.follower-options h4 {
    font-size: 1.3rem;
    color: #ffffff;
    margin-bottom: 20px;
    text-align: center;
}

.option-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.option-card {
    background: #1a1a1a;
    border: 2px solid #333333;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.option-card:hover {
    border-color: #ff0050;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 0, 80, 0.3);
}

.option-card.selected {
    border-color: #ff0050;
    background: linear-gradient(135deg, rgba(255, 0, 80, 0.1), rgba(0, 0, 0, 0.3));
    transform: scale(1.05);
}

.option-number {
    font-size: 1.5rem;
    font-weight: 800;
    background: linear-gradient(45deg, #ff0050, #ff4081);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 5px;
}

.option-label {
    font-size: 0.9rem;
    color: #a0a0a0;
    font-weight: 600;
}

.option-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: linear-gradient(45deg, #ff0050, #ff4081);
    color: white;
    font-size: 0.65rem;
    font-weight: 700;
    padding: 3px 6px;
    border-radius: 8px;
    transform: rotate(12deg);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    z-index: 1;
}

/* Add Comment Section */
.add-comment-section {
    background: #1a1a1a;
    border: 1px solid #333333;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
}

.comment-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.comment-user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.comment-user-info .comment-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid #ff0050;
}

.comment-user-info span {
    font-weight: 600;
    color: #ffffff;
}

.comment-input-group {
    display: flex;
    gap: 10px;
    align-items: flex-end;
}

.comment-input-group textarea {
    flex: 1;
    min-height: 80px;
    padding: 12px;
    border: 2px solid #333333;
    border-radius: 10px;
    background: #1a1a1a;
    color: #ffffff;
    font-family: inherit;
    font-size: 0.9rem;
    resize: vertical;
    transition: border-color 0.3s ease;
}

.comment-input-group textarea:focus {
    outline: none;
    border-color: #ff0050;
}

.comment-input-group button {
    padding: 12px 20px;
    white-space: nowrap;
}

/* New Comment Animation */
.new-comment {
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.5s ease;
    border-left: 3px solid #ff0050;
    background: rgba(255, 0, 80, 0.1);
    margin-bottom: 15px;
    border-radius: 10px;
    padding: 15px;
}

.new-comment.comment-visible {
    opacity: 1;
    transform: translateY(0);
}
