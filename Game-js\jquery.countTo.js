/*
 * jQuery countTo Plugin
 * Simple placeholder for countTo functionality
 */
(function ($) {
    $.fn.countTo = function (options) {
        options = options || {};
        
        return $(this).each(function () {
            var $this = $(this);
            var from = parseInt($this.data('from')) || 0;
            var to = parseInt($this.data('to')) || parseInt($this.text()) || 100;
            var speed = parseInt(options.speed) || 1000;
            var refreshInterval = parseInt(options.refreshInterval) || 100;
            var decimals = parseInt(options.decimals) || 0;
            var formatter = options.formatter || function (value, options) {
                return value.toFixed(options.decimals);
            };
            var onUpdate = options.onUpdate || function () {};
            var onComplete = options.onComplete || function () {};
            
            var loops = Math.ceil(speed / refreshInterval);
            var increment = (to - from) / loops;
            var current = from;
            var loopCount = 0;
            
            var timer = setInterval(function () {
                current += increment;
                loopCount++;
                
                if (loopCount >= loops) {
                    clearInterval(timer);
                    current = to;
                    onComplete.call($this[0]);
                }
                
                $this.text(formatter(current, options));
                onUpdate.call($this[0], current);
            }, refreshInterval);
        });
    };
})(jQuery);
