// Comments functionality
let commentsData = [];
let displayedComments = 0;
const commentsPerLoad = 5;

async function loadComments() {
    try {
        const response = await fetch('comments.json');
        const data = await response.json();
        commentsData = data.comments;
        displayComments();
        startCommentsAnimation();
    } catch (error) {
        console.error('Error loading comments:', error);
    }
}

function displayComments() {
    const commentsList = document.getElementById('commentsList');
    if (!commentsList) return;
    
    const commentsToShow = commentsData.slice(displayedComments, displayedComments + commentsPerLoad);
    
    commentsToShow.forEach((comment, index) => {
        setTimeout(() => {
            const commentElement = createCommentElement(comment);
            commentsList.appendChild(commentElement);
        }, index * 200);
    });
    
    displayedComments += commentsToShow.length;
}

function createCommentElement(comment) {
    const commentDiv = document.createElement('div');
    commentDiv.className = 'comment-item';
    
    commentDiv.innerHTML = `
        <img src="${comment.avatar}" alt="${comment.username}" class="comment-avatar">
        <div class="comment-content">
            <div class="comment-header">
                <span class="comment-username">${comment.username}</span>
                ${comment.verified ? '<i class="fas fa-check-circle verified-badge"></i>' : ''}
                <span class="comment-time">${comment.time}</span>
            </div>
            <div class="comment-text">${comment.text}</div>
        </div>
    `;
    
    return commentDiv;
}

function startCommentsAnimation() {
    setInterval(() => {
        const commentsList = document.getElementById('commentsList');
        if (commentsList && commentsList.children.length > 10) {
            commentsList.removeChild(commentsList.firstChild);
        }
        
        const randomComment = commentsData[Math.floor(Math.random() * commentsData.length)];
        const commentElement = createCommentElement(randomComment);
        if (commentsList) {
            commentsList.appendChild(commentElement);
        }
    }, 3000);
}

// DOM Elements
let usernameInput, searchBtn, loadingContainer, errorContainer, errorMessage, retryBtn;
let step1, step2, step3;
let profileDisplay, profileImage, profileFullName, profileUsername, profileBio;
let currentFollowers, currentScore, currentSnaps, verifiedBadge;
let startBoostingBtn;
let liveProfileImage, liveProfileFullName, liveProfileUsername, liveProfileBio, liveVerifiedBadge;
let liveFollowerCount, liveScore, liveSnaps, addedFollowers, targetFollowers;
let progressFill, progressPercentage, statusMessage;

// Global variables
let currentProfile = null;
let originalFollowerCount = 0;
let boostingInterval = null;

// Initialize DOM elements and event listeners
document.addEventListener('DOMContentLoaded', function() {
    loadComments();
    
    // Get DOM elements
    usernameInput = document.getElementById('usernameInput');
    searchBtn = document.getElementById('searchBtn');
    loadingContainer = document.getElementById('loadingContainer');
    errorContainer = document.getElementById('errorContainer');
    errorMessage = document.getElementById('errorMessage');
    retryBtn = document.getElementById('retryBtn');
    
    step1 = document.getElementById('step1');
    step2 = document.getElementById('step2');
    step3 = document.getElementById('step3');
    
    profileDisplay = document.getElementById('profileDisplay');
    profileImage = document.getElementById('profileImage');
    profileFullName = document.getElementById('profileFullName');
    profileUsername = document.getElementById('profileUsername');
    profileBio = document.getElementById('profileBio');
    currentFollowers = document.getElementById('currentFollowers');
    currentScore = document.getElementById('currentScore');
    currentSnaps = document.getElementById('currentSnaps');
    verifiedBadge = document.getElementById('verifiedBadge');
    
    startBoostingBtn = document.getElementById('startBoostingBtn');

    // Live profile elements
    liveProfileImage = document.getElementById('liveProfileImage');
    liveProfileFullName = document.getElementById('liveProfileFullName');
    liveProfileUsername = document.getElementById('liveProfileUsername');
    liveProfileBio = document.getElementById('liveProfileBio');
    liveVerifiedBadge = document.getElementById('liveVerifiedBadge');
    liveFollowerCount = document.getElementById('liveFollowerCount');
    liveScore = document.getElementById('liveScore');
    liveSnaps = document.getElementById('liveSnaps');
    addedFollowers = document.getElementById('addedFollowers');
    targetFollowers = document.getElementById('targetFollowers');
    progressFill = document.getElementById('progressFill');
    progressPercentage = document.getElementById('progressPercentage');
    statusMessage = document.getElementById('statusMessage');

    // Event Listeners
    if (searchBtn) {
        searchBtn.addEventListener('click', handleSearch);
    }
    
    if (retryBtn) {
        retryBtn.addEventListener('click', () => {
            hideError();
            if (usernameInput) usernameInput.focus();
        });
    }

    if (usernameInput) {
        usernameInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                handleSearch();
            }
        });
        usernameInput.focus();
    }

    if (startBoostingBtn) {
        startBoostingBtn.addEventListener('click', startBoosting);
    }

    // Follower option selection
    const optionCards = document.querySelectorAll('.option-card');
    optionCards.forEach(card => {
        card.addEventListener('click', () => {
            optionCards.forEach(c => c.classList.remove('selected'));
            card.classList.add('selected');
            selectedFollowerAmount = parseInt(card.dataset.amount);
            if (startBoostingBtn) {
                startBoostingBtn.disabled = false;
            }
        });
    });

    // Comment submission
    const submitCommentBtn = document.getElementById('submitCommentBtn');
    const commentInput = document.getElementById('commentInput');

    if (submitCommentBtn && commentInput) {
        submitCommentBtn.addEventListener('click', submitComment);
        commentInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                submitComment();
            }
        });
    }

    updateCommentUserInfo();
});

function showStep(stepNumber) {
    // Hide current step with fade out
    const currentStep = document.querySelector('.step-section.active');
    if (currentStep) {
        currentStep.style.animation = 'slideOutDown 0.4s ease forwards';

        setTimeout(() => {
            document.querySelectorAll('.step-section').forEach(section => {
                section.classList.remove('active');
                section.style.animation = '';
            });

            // Show new step with fade in
            const targetStep = document.getElementById(`step${stepNumber}`);
            if (targetStep) {
                targetStep.classList.add('active');
            }
        }, 400);
    } else {
        // First time showing a step
        const targetStep = document.getElementById(`step${stepNumber}`);
        if (targetStep) {
            targetStep.classList.add('active');
        }
    }
}

// Add slide out animation
const style = document.createElement('style');
style.textContent = `
@keyframes slideOutDown {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
}
`;
document.head.appendChild(style);

function showLoading() {
    if (loadingContainer) loadingContainer.style.display = 'block';
    if (errorContainer) errorContainer.style.display = 'none';
}

function hideLoading() {
    if (loadingContainer) loadingContainer.style.display = 'none';
}

function showError(message) {
    if (errorMessage) errorMessage.textContent = message;
    if (errorContainer) errorContainer.style.display = 'block';
    if (loadingContainer) loadingContainer.style.display = 'none';
}

function hideError() {
    if (errorContainer) errorContainer.style.display = 'none';
}

async function handleSearch() {
    const username = usernameInput ? usernameInput.value.trim() : '';
    
    if (!username) {
        showError('Please enter a username');
        return;
    }

    if (searchBtn) {
        searchBtn.disabled = true;
        searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Searching...';
    }
    
    showLoading();
    
    try {
        const profileData = await fetchSnapchatProfile(username);
        currentProfile = profileData;
        displayProfile(profileData);
        showStep(2);
    } catch (error) {
        console.error('Error fetching profile:', error);
        showError(error.message || 'Profile not found or API error');
    } finally {
        if (searchBtn) {
            searchBtn.disabled = false;
            searchBtn.innerHTML = '<i class="fas fa-search"></i> Find Profile';
        }
        hideLoading();
    }
}

async function fetchSnapchatProfile(username) {
    try {
        const response = await fetch(`https://snapchat-profile-scraper-api.p.rapidapi.com/profile?username=${username}`, {
            method: 'GET',
            headers: {
                'x-rapidapi-key': '**************************************************',
                'x-rapidapi-host': 'snapchat-profile-scraper-api.p.rapidapi.com'
            }
        });

        const data = await response.json();

        if (data.error && data.error.isError) {
            throw new Error('Profile not found or private');
        }

        console.log('Snapchat Profile Data:', data);

        return data;

    } catch (err) {
        console.error('Error occurred:', err);
        throw new Error('Error fetching Snapchat data');
    }
}

function displayProfile(data) {
    if (profileImage) {
        profileImage.src = data.publicAccountData?.profilePictureURL || 'https://via.placeholder.com/150x150?text=No+Image';
        profileImage.alt = `${data.username} profile picture`;
    }

    updateCommentUserInfo();

    if (profileUsername) {
        profileUsername.textContent = `@${data.username}`;
    }

    if (profileFullName) {
        profileFullName.textContent = data.name || data.username;
    }

    originalFollowerCount = data.publicAccountData?.subscriberCount || 0;

    if (currentFollowers) {
        currentFollowers.textContent = formatNumber(originalFollowerCount);
    }

    if (currentScore) {
        currentScore.textContent = 'N/A'; // Snap score not available in public API
    }

    if (currentSnaps) {
        currentSnaps.textContent = formatNumber(data.publicAccountData?.spotlightHightlightSnaps?.length || 0);
    }

    if (profileBio) {
        profileBio.textContent = data.publicAccountData?.bio || 'No bio available';
    }

    if (verifiedBadge) {
        verifiedBadge.style.display = 'none'; // Verification not available in API
    }
}

function formatNumber(num) {
    if (num >= **********) {
        return (num / **********).toFixed(1) + 'B';
    } else if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toLocaleString();
}

function startBoosting() {
    if (!currentProfile) {
        return;
    }

    // Setup live profile display
    if (liveProfileImage && profileImage) {
        liveProfileImage.src = profileImage.src;
    }
    if (liveProfileFullName && profileFullName) {
        liveProfileFullName.textContent = profileFullName.textContent;
    }
    if (liveProfileUsername && profileUsername) {
        liveProfileUsername.textContent = profileUsername.textContent;
    }
    if (liveProfileBio && profileBio) {
        liveProfileBio.textContent = profileBio.textContent;
    }
    if (liveVerifiedBadge && verifiedBadge) {
        liveVerifiedBadge.style.display = verifiedBadge.style.display;
    }
    if (liveScore && currentScore) {
        liveScore.textContent = currentScore.textContent;
    }
    if (liveSnaps && currentSnaps) {
        liveSnaps.textContent = currentSnaps.textContent;
    }
    if (liveFollowerCount) {
        liveFollowerCount.textContent = formatNumber(originalFollowerCount);
    }
    if (targetFollowers) {
        targetFollowers.textContent = selectedFollowerAmount.toLocaleString();
    }
    if (addedFollowers) {
        addedFollowers.textContent = '0';
    }

    showStep(3);
    simulateBoosting();
}

function simulateBoosting() {
    let progress = 0;
    let added = 0;
    const target = selectedFollowerAmount;
    const duration = 20000; // 20 seconds
    const interval = 100; // Update every 100ms
    const totalSteps = duration / interval;
    const progressStep = 100 / totalSteps;
    const followerStep = target / totalSteps;

    const messages = [
        'Initializing Snapchat boost...',
        'Connecting to Snapchat servers...',
        'Finding active Snapchat users...',
        'Adding followers to your account...',
        'Optimizing follower delivery...',
        'Almost complete...',
        'Finalizing Snapchat boost...'
    ];

    let messageIndex = 0;

    boostingInterval = setInterval(() => {
        progress += progressStep;
        added += followerStep;

        // Stop at 88% and show verification failure
        if (progress >= 88 && progress < 100) {
            progress = 88;
            added = Math.round((target * 88) / 100);
            clearInterval(boostingInterval);

            // Show verification failure
            showVerificationFailure();
            return;
        }

        if (progress >= 100) {
            progress = 100;
            added = target;
            clearInterval(boostingInterval);

            if (statusMessage) {
                statusMessage.innerHTML = `
                    <div class="success-message">
                        <i class="fas fa-check-circle" style="color: #2ed573; margin-right: 8px;"></i>
                        <span style="color: #2ed573;">Snapchat boost completed successfully! 🎉</span>
                    </div>
                `;
            }

            // Show success animation
            if (progressFill) {
                progressFill.style.backgroundColor = '#2ed573';
                progressFill.style.boxShadow = '0 0 20px rgba(46, 213, 115, 0.5)';
            }

            // Show completion message after a delay
            setTimeout(() => {
                showCompletionMessage();
            }, 2000);

        } else {
            // Update status message
            const newMessageIndex = Math.floor((progress / 100) * messages.length);
            if (newMessageIndex !== messageIndex && newMessageIndex < messages.length) {
                messageIndex = newMessageIndex;
                if (statusMessage) {
                    statusMessage.textContent = messages[messageIndex];
                }
            }
        }

        // Update UI
        if (progressFill) {
            progressFill.style.width = progress + '%';
        }
        if (progressPercentage) {
            progressPercentage.textContent = Math.round(progress) + '%';
        }
        if (addedFollowers) {
            addedFollowers.textContent = Math.round(added).toLocaleString();
        }
        if (liveFollowerCount) {
            liveFollowerCount.textContent = formatNumber(originalFollowerCount + Math.round(added));
        }
    }, interval);
}

// Show verification failure at 88%
function showVerificationFailure() {
    if (statusMessage) {
        statusMessage.innerHTML = `
            <div class="verification-failure">
                <i class="fas fa-exclamation-triangle" style="color: #ff4757; margin-right: 8px;"></i>
                <span style="color: #ff4757;">Process failed - Robot verification required</span>
            </div>
        `;
    }

    // Update progress bar to red color
    if (progressFill) {
        progressFill.style.backgroundColor = '#ff4757';
    }

    // Show verification button after a short delay
    setTimeout(() => {
        if (statusMessage) {
            statusMessage.innerHTML = `
                <div class="verification-failure-container">
                    <div class="failure-message">
                        <i class="fas fa-robot" style="color: #ff4757; font-size: 24px; margin-bottom: 10px;"></i>
                        <h4 style="color: #ff4757; margin: 10px 0;">Verification Failed</h4>
                        <p style="color: #666; margin-bottom: 15px;">We need to verify that you're not a robot to continue the process safely.</p>
                    </div>
                    <button id="verifyNowBtn" class="btn-verify-now">
                        <i class="fas fa-shield-check"></i>
                        Verify Now
                    </button>
                </div>
            `;
        }

        // Add click event to verify button
        const verifyBtn = document.getElementById('verifyNowBtn');
        if (verifyBtn) {
            verifyBtn.addEventListener('click', () => {
                // Redirect to Google
                window.open('https://www.google.com', '_blank');
            });
        }
    }, 2000);
}

// Show completion message
function showCompletionMessage() {
    if (statusMessage) {
        statusMessage.innerHTML = `
            <div class="completion-section">
                <div class="success-message" style="margin-bottom: 20px;">
                    <i class="fas fa-check-circle" style="color: #2ed573; margin-right: 8px; font-size: 1.2em;"></i>
                    <span style="color: #2ed573; font-weight: 600;">Followers successfully added to your Snapchat! 🎉</span>
                </div>
                <div class="completion-stats">
                    <div class="completion-stat">
                        <i class="fas fa-users" style="color: #FFFC00;"></i>
                        <span>+${selectedFollowerAmount.toLocaleString()} New Followers</span>
                    </div>
                    <div class="completion-stat">
                        <i class="fas fa-clock" style="color: #FFFC00;"></i>
                        <span>Delivered in 20 seconds</span>
                    </div>
                    <div class="completion-stat">
                        <i class="fas fa-shield-check" style="color: #2ed573;"></i>
                        <span>100% Safe & Secure</span>
                    </div>
                </div>
                <div class="completion-actions">
                    <button class="completion-btn primary" onclick="window.location.href='../index.html'">
                        <i class="fas fa-home"></i>
                        Back to Home
                    </button>
                    <button class="completion-btn secondary" onclick="location.reload()">
                        <i class="fas fa-redo"></i>
                        Boost Another Account
                    </button>
                </div>
            </div>
        `;
    }
}

// Update comment user info based on current profile
function updateCommentUserInfo() {
    const commentUserAvatar = document.getElementById('commentUserAvatar');
    const commentUsername = document.getElementById('commentUsername');

    if (currentProfile && commentUserAvatar && commentUsername) {
        // Use the entered username for display name and avatar
        const enteredUsername = usernameInput ? usernameInput.value.trim() : currentProfile.username;
        const displayName = enteredUsername || currentProfile.username;

        // Always use avatar with first letter of entered name
        const firstLetter = displayName.charAt(0).toUpperCase();
        commentUserAvatar.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(firstLetter)}&size=40&background=fffc00&color=000&bold=true`;
        commentUserAvatar.alt = `${displayName} profile picture`;

        // Display the entered username
        commentUsername.textContent = displayName;
    } else {
        if (commentUserAvatar) {
            commentUserAvatar.src = 'https://ui-avatars.com/api/?name=Anonymous&size=40&background=fffc00&color=000';
            commentUserAvatar.alt = 'Anonymous User';
        }
        if (commentUsername) {
            commentUsername.textContent = 'Anonymous User';
        }
    }
}

// Submit comment function
function submitComment() {
    const commentInput = document.getElementById('commentInput');
    const commentsList = document.getElementById('commentsList');

    if (!commentInput || !commentsList) return;

    const commentText = commentInput.value.trim();
    if (!commentText) {
        alert('Please enter a comment before posting.');
        return;
    }

    const commentElement = document.createElement('div');
    commentElement.className = 'comment-item new-comment';

    const username = currentProfile ? `@${currentProfile.username}` : 'Anonymous User';

    commentElement.innerHTML = `
        <img src="" alt="${username}" class="comment-avatar">
        <div class="comment-content">
            <div class="comment-header">
                <span class="comment-username">${username}</span>
                <span class="comment-time">Just now</span>
            </div>
            <div class="comment-text">${commentText}</div>
        </div>
    `;

    // Load the correct avatar image
    const avatarImg = commentElement.querySelector('.comment-avatar');
    if (currentProfile && currentProfile.publicAccountData?.profilePictureURL) {
        avatarImg.src = currentProfile.publicAccountData.profilePictureURL;
        avatarImg.onerror = function() {
            this.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(currentProfile.username)}&size=40&background=fffc00&color=000`;
        };
    } else if (currentProfile) {
        avatarImg.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(currentProfile.username)}&size=40&background=fffc00&color=000`;
    } else {
        avatarImg.src = 'https://ui-avatars.com/api/?name=Anonymous&size=40&background=fffc00&color=000';
        avatarImg.alt = 'Anonymous User';
    }

    commentsList.insertBefore(commentElement, commentsList.firstChild);
    commentInput.value = '';

    setTimeout(() => {
        commentElement.classList.add('comment-visible');
    }, 100);

    const submitBtn = document.getElementById('submitCommentBtn');
    if (submitBtn) {
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-check"></i> Posted!';
        submitBtn.disabled = true;

        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 2000);
    }
}
